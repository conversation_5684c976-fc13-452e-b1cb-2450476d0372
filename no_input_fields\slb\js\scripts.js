function validateForm(e) {
    const username = document.querySelector('#username')
    // const email = document.querySelector('#email')
    const contact = document.querySelector('#contact')
    const errorMessage = document.querySelector('#errorMessage')
    const form = document.querySelector("#the-form");
    if(username.value.length === 0){
        clearError();
        username.classList.add("error")
        username.focus()
        errorMessage.style.opacity = "1";
        return false
    // } else if(email.value.length === 0) {
    //     clearError();
    //     email.classList.add("error")
    //     email.focus()
    //     errorMessage.style.opacity = "1";
    //     return false
    // } else if (!validateEmail(email.value)) {
    //     clearError();
    //     email.classList.add("error")
    //     errorMessage.innerText = 'Please enter a valid email'
    //     email.focus()
    //     errorMessage.style.opacity = "1";
    //     return false
    } else if(contact.value.length === 0) {
        clearError();
        contact.classList.add("error")
        contact.focus()
        errorMessage.style.opacity = "1";
        return false
    } else if(contact.value.length < 10) {
        clearError();
        contact.classList.add("error")
        errorMessage.innerText = 'Please enter a valid contact number'
        contact.focus()
        errorMessage.style.opacity = "1";
        return false
    } else {
            clearError();
            form.submit();
            return true;
    }
  }

function clearError() {
    const username = document.querySelector('#username')
    // const email = document.querySelector('#email')
    const contact = document.querySelector('#contact')

    const errorMessage = document.querySelector('#errorMessage')
    errorMessage.innerText = "Please fill in all the fields"
    username.classList.remove("error")
    // email.classList.remove("error")
    contact.classList.remove("error")
    errorMessage.style.opacity = "0";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}