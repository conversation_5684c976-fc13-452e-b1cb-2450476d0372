@import url('https://fonts.googleapis.com/css?family=Montserrat:400,600,700i');
body {
	font-family: 'Montserrat', sans-serif;
	color:#2b2e34;
	background: #ffffff;
}

.minH-100vh {
	min-height:100vh;
}
.loginSection {
	margin: 0 auto;
	padding: 10px 15px 20px;
	width: 100%;
	max-width: 410px;
}
.loginSection h1 {
	font-size: 3.25rem;
	font-weight: 700;
	font-style: italic;
	line-height: 3.75rem;
	padding-bottom: 0.625rem;
}
.loginSection p {
	font-size: 1.375rem;
	font-weight: 600;
	line-height: normal;
}

.fldStyle, .btnStyle {
	height:3.125rem;
	border:none;
	-webkit-border-radius:0;
	-moz-border-radius:0;
	border-radius:0;
}
.btnStyle {
	background:#1d1c19;
	color:#fff;
	text-transform:uppercase;
	font-weight:600;
}
.rightIconFLd {
	position: relative;
}
.rightIconFLd .fldStyle {
	padding-right: 1.875rem;
}
.rightIconFLd .fldIcon {
	position: absolute;
	right: 0.625rem;
	top: 50%;
	transform: translate(0,-50%);
}
.fldIcon > svg {
	display:block;
}
.loginForm {
	padding-top: 1.25rem;
}

.mtnIcon {
	text-align: right;
	padding-top: 1.75rem;
}
.mtnIcon > img {
	max-width: 3.75rem;
}


@media screen and (max-width:767px) {
.loginSection h1 {
	font-size:  2.5rem;
	line-height: 2.875rem;
}
.loginSection p {
	font-size: 1rem;
	margin-bottom: 0.625rem;
}
.fldStyle, .btnStyle {
	height: 44px;
	font-size: 0.875rem;
}
.loginForm {
	padding-top: 0.625rem;
}
.mtnIcon {
	padding-top: 1rem;
}
}


