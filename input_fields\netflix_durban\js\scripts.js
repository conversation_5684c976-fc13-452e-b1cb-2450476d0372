function validateForm(e) {
    const name = document.querySelector('#name')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')
    const form = document.querySelector("#the-form");

    const phoneNumberValidation = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./[0-9]{9,10}]*$/
    if(name.value.length === 0){
        clearError();
        name.classList.add("error")
        name.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else {
        clearError();
        form.submit();
        return true;
    }
}
function clearError() {
    const name = document.querySelector('#name')
    const errorMessageContainer = document.querySelector('#messageContainer')

    const errorMessage = document.querySelector('#errorMessage')
    errorMessage.innerHTML = "Please fill in your name &amp; surname"
    name.classList.remove("error")
    errorMessage.style.opacity = "0";
    errorMessageContainer.style.display = "none";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}

