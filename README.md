# qcw_hostpot_login_template

The templates used for mikrotik hotspots are stored here in two categories:
  * no_input_fields
  * input_fields

## no_input_fields

These templates are the more basic in their functionality and just have a connect button that allows the user to proceed

## input_fields

These templates are the more advanced in their functionality and have a mix of input fields that can be required or optional and the user needs to fill them in before proceeding. These results are collected and stored in a mongo collection when the user proceeds, most times to be given to the organiser of the event

## Creating a template

Templates are created under the desired category with as a folder with the desired name (most easily done by just duplicating an existing template)

## Uploading

Templates are uploaded to [login.quickconnect.co.za](http://login.quickconnect.co.za/) as a zip with the same name as the folder `(example_template -> example_template.zip)`

You start by entering the event name; this would be the same as the template name `(example_template)`. If it is a new template you can just go through the process, but if it already exists you can choose to extract the existing template or overwrite the existing template.

You will also be given the option of setting both redirect and returning redirect urls if need them for the event

The hotspot can then be found at [login.quickconnect.co.za/example_template](http://login.quickconnect.co.za/example_template)

## Layout

The templates have a basic set layout that needs to be adhered to due to the processing that our server does when uploading a template. 

Base folder structure:
* template
  * css
    * main.css
  * js
    * scripts.js
  * images
    * image.png
  * vendor
    * any third party libraries used
  * index.html

All files referenced inside the index.html should be absolute paths within that folder:

```html
<link rel="stylesheet" type="text/css" href="css/main.css">

<img src="images/image.png">
```