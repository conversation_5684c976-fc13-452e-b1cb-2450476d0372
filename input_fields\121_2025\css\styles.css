@font-face {
    font-family: 'roboto';
    src: url("roboto.ttf");
}

@font-face {
    font-family: 'roboto light';
    src: url("roboto-light.ttf");
}

:root {
    font-family: 'roboto';
}

*, *::after, *::before {
    padding: 0;
    margin: 0;
}

html, body {
    height: 100%;
    background-image: url(../images/bg.jpg);
    background-position: cnter center;
    background-size: cover;
    background-repeat: no-repeat;
}

.wrapper {
    display: flex;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.624);
    overflow: auto;
}

#the-form {
    margin: auto;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.455);
    color: white;
    max-width: 500px;
    width: 100%;
    transition: all 0.2s ease;
    border-radius: 0.8rem;
    display: grid;
    grid-template-columns: 1fr;
}

.header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4rem;
}

.image__logo {
    max-width: 60%;
}

.image__logo--ats {
    max-width: 30%;
    height: 100%;
}

#the-form > h1{
    font-family: 'roboto light';
    font-weight: 100;
    font-size: calc(1rem * 2.3);
    text-align: center;
    margin-bottom: 4rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.form-group label {
    font-size: calc(1rem * 0.9);
    margin-bottom: 0.6rem;
}

.form-group input {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.49);
    padding: 0.2rem;
    width: 70%;
    color: white;
}

.form-group input:focus {
    outline: none;
}

.form-group.password {
    margin-top: 2rem;
}

.login {
    margin: 1.5rem auto 6rem auto;
    display: block;
    background-color: transparent;
    border: none;
    cursor: pointer;
}

#messageContainer {
    margin-top: 0.5rem;
    text-align: center;
    color: rgb(255, 0, 0);
}

#errorMessage {
}

@media screen and (max-width: 550px) {
    #the-form {
        margin: 0.5rem;
    }
}