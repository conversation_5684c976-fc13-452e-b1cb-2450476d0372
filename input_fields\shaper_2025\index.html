<!DOCTYPE html>
<html lang="en">
<head>
<title>Login</title>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
<!--===============================================================================================-->
<link rel="icon" type="image/png" href="images/icons/favicon.ico"/>
<!--===============================================================================================-->
<!-- <link rel="stylesheet" type="text/css" href="vendor/bootstrap/css/bootstrap.min.css"> -->
<!--===============================================================================================-->

<!--===============================================================================================-->
<!--===============================================================================================-->
<!-- <link rel="stylesheet" type="text/css" href="css/util.css">
<link rel="stylesheet" type="text/css" href="css/main.css"> -->
<link rel="stylesheet" type="text/css" href="css/styles.css">
<!-- Vendor CSS-->
<link href="vendor/mdi-font/css/material-design-iconic-font.min.css" rel="stylesheet" media="all">
<!-- <link href="vendor/select2/select2.min.css" rel="stylesheet" media="all"> -->
<!-- <link href="vendor/datepicker/daterangepicker.css" rel="stylesheet" media="all"> -->
<!--===============================================================================================-->

</head>
<body>
<div class="wrapper">
    <div class="section-header">
        <div class="shaper-logo">
            <img src="images/shaper-logo.png" class="main-logo">
        </div>
        
        <div class="text-cta">
            Enjoy free WiFi on us.
        </div>

        <div class="text-other">
            Your journey to knowledge starts here.
        </div>
    </div>

    <div class="section-content">
        <form class="" method="POST" id="the-form" onsubmit="return validateForm(event)">
            <div class="form-group name">
                <label class="control-label" for="name">Name</label>
                <input class="form-control" type="text" name="name" id="name">
            </div>
            <div class="form-group surname">
                <label class="control-label" for="surname">Surname</label>
                <input class="form-control" name="surname" id="surname">
            </div>
            <div class="form-group email">
                <label class="control-label" for="email">Email</label>
                <input class="form-control" name="email" id="email" type="email">
            </div>
            
            <div id="messageContainer">    
                <p id="errorMessage" style="opacity: 0;">Please fill in all the fields</p>
            </div>

            <button class="login" type="submit">
                Connect
            </button>
        </form>
        
        <div class="section-name">
            <h2><a href="shaper.co.ca">shaper.co.za</a></h2>
        </div>
    </div>
</div>
<script src="js/scripts.js"></script>
<script src="vendor/jquery/jquery.min.js"></script> 
<script type="text/javascript">
	/* First option in SELECT tag need to be BLANK */
/*$('.form-control').on('focus blur', function (e) {
     $(this).parents('.form-group').toggleClass('focused', (e.type === 'focus' || this.value.length > 0));
}).trigger('blur');*/

/* First option in SELECT tag don't need to be BLANK */
$('.form-control').on('focus blur change', function (e) {
	var $currEl = $(this);
  
  if($currEl.is('select')) {
  	if($currEl.val() === $("option:first", $currEl).val()) {
    	$('.control-label', $currEl.parent()).animate({opacity: 0}, 240);
      $currEl.parent().removeClass('focused');
    } else {
    	$('.control-label', $currEl.parent()).css({opacity: 1});
    	$currEl.parents('.form-group').toggleClass('focused', ((e.type === 'focus' || this.value.length > 0) && ($currEl.val() !== $("option:first", $currEl).val())));
    }
  } else {
  	$currEl.parents('.form-group').toggleClass('focused', (e.type === 'focus' || this.value.length > 0));
  }
}).trigger('blur');
</script>
</body>
</html>