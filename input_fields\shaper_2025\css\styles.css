@font-face {
    font-family: 'roboto';
    src: url("roboto.ttf");
}

@font-face {
    font-family: 'roboto light';
    src: url("roboto-light.ttf");
}

:root {
    font-family: 'roboto';
}

*, *::after, *::before {
    padding: 0;
    margin: 0;
}

html, body {
    height: 100%;
    background-image: url(../images/background-image.webp);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

.wrapper {
    display: flex;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    overflow: auto;
}

#the-form {
    margin: auto;
    padding: 3rem 2rem;
    background-color: rgba(255, 255, 255, 0.95);
    color: #333;
    max-width: 450px;
    width: 100%;
    transition: all 0.2s ease;
    border-radius: 1rem;
    display: grid;
    grid-template-columns: 1fr;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.image__logo {
    max-width: 200px;
    height: auto;
}

#the-form > h1{
    font-family: 'roboto';
    font-weight: 300;
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    letter-spacing: 1px;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
    width: 100%;
}

.form-group label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 500;
}

.form-group input {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    width: 100%;
    color: #333;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    background-color: #fff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group.password {
    margin-top: 0;
}

.login {
    margin: 2rem auto 1rem auto;
    display: block;
    background-color: #007bff;
    border: none;
    cursor: pointer;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.login:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.login img {
    display: none;
}

#messageContainer {
    margin-top: 1rem;
    text-align: center;
    color: #dc3545;
}

#errorMessage {
    font-size: 0.9rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

@media screen and (max-width: 768px) {
    #the-form {
        margin: 1rem;
        padding: 2rem 1.5rem;
        max-width: none;
    }

    .image__logo {
        max-width: 150px;
    }

    #the-form > h1 {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .login {
        margin: 1.5rem auto 0.5rem auto;
        width: 100%;
    }
}

@media screen and (max-width: 480px) {
    #the-form {
        margin: 0.5rem;
        padding: 1.5rem 1rem;
    }

    .image__logo {
        max-width: 120px;
    }

    #the-form > h1 {
        font-size: 1.25rem;
    }
}