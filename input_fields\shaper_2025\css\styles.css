@font-face {
    font-family: 'roboto';
    src: url("roboto.ttf");
}

@font-face {
    font-family: 'roboto light';
    src: url("roboto-light.ttf");
}

:root {
    font-family: 'roboto';
}

*, *::after, *::before {
    padding: 0;
    margin: 0;
}

html, body {
    height: 100%;
    background-image: url(../images/background-image.webp);
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

.wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    overflow: auto;
    position: relative;
    align-items: center;
    justify-content: space-between;
    padding: 2rem 1rem;
    box-sizing: border-box;
    overflow: hidden;
}

.wrapper::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -35%);
    width: 1000px;
    height: 1000px;
    background-image: url(../images/shaper-logo-blocks.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 1;
    z-index: 1;
    pointer-events: none;
}

#the-form {
    padding: 2.5rem 2rem;
    background-color: rgba(255, 255, 255, 0.95);
    color: #333;
    max-width: 400px;
    width: 100%;
    transition: all 0.2s ease;
    border-radius: 1rem;
    display: grid;
    grid-template-columns: 1fr;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
    margin: 1rem 0;
}

/* Section Header Styles */
.section-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    z-index: 3;
    color: white;
    margin-bottom: 2rem;
}

.shaper-logo {
    margin-bottom: 1.5rem;
}

.main-logo {
    max-width: 200px;
    height: auto;
    filter: drop-shadow(0 2px 10px rgba(0, 0, 0, 0.3));
}

.text-cta {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.text-other {
    font-size: 1rem;
    font-weight: 300;
    opacity: 0.9;
    letter-spacing: 0.3px;
}

/* Legacy styles - hidden */
.logo-container {
    display: none;
}

.header {
    display: none;
}

.image__logo {
    max-width: 200px;
    height: auto;
}

#the-form > h1{
    font-family: 'roboto';
    font-weight: 300;
    font-size: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    letter-spacing: 1px;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
    width: 100%;
}

.form-group label {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 500;
}

.form-group input {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    width: 100%;
    color: #333;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    background-color: #fff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group.password {
    margin-top: 0;
}

.login {
    margin: 2rem auto 1rem auto;
    display: block;
    background-color: #FF7900;
    border: none;
    cursor: pointer;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.login:hover {
    background-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.login img {
    display: none;
}

/* Section Name (Footer) Styles */
.section-name {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    margin-top: 2rem;
}

.section-name h2 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 300;
}

.section-name a {
    color: black;
    text-decoration: none;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    opacity: 0.9;
}

.section-name a:hover {
    opacity: 1;
    text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
}

#messageContainer {
    margin-top: 1rem;
    text-align: center;
    color: #dc3545;
}

#errorMessage {
    font-size: 0.9rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.text-cta {
    color: #C755FF;
}

.text-other {
    color: black;
}

@media screen and (max-width: 768px) {
    .wrapper {
        background-color: #fff;
    }

    .wrapper::before {
        background-image: url(../images/shaper-logo-blocks-mobile.png);
    }

    .wrapper {
        padding: 1.5rem 1rem;
    }

    .section-header {
        margin-bottom: 1.5rem;
    }

    .main-logo {
        max-width: 160px;
    }

    .text-cta {
        font-size: 1.3rem;
    }

    .text-other {
        font-size: 0.9rem;
    }

    #the-form {
        padding: 2rem 1.5rem;
        /* max-width: calc(100vw - 4rem); */
        margin: 0.5rem 1rem;
    }

    #the-form > h1 {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .login {
        margin: 1.5rem auto 0.5rem auto;
        width: 100%;
    }

    .section-name {
        margin-top: 1.5rem;
    }

    .section-name h2 {
        font-size: 1.1rem;
    }
}

@media screen and (max-width: 480px) {
    .wrapper::before {
    }

    .wrapper {
        padding: 1rem 0.5rem;
    }

    .section-header {
        margin-bottom: 1rem;
    }

    .main-logo {
        max-width: 140px;
    }

    .text-cta {
        font-size: 1.1rem;
    }

    .text-other {
        font-size: 0.85rem;
    }

    #the-form {
        padding: 1.5rem 1rem;
        margin: 0.5rem 1rem;
    }

    #the-form > h1 {
        font-size: 1.25rem;
    }

    .section-name {
        margin-top: 1rem;
    }

    .section-name h2 {
        font-size: 1rem;
    }
}