function validateForm(e) {
    const name = document.querySelector('#name')
    const email = document.querySelector('#email')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')
    const form = document.querySelector("#the-form");

    if(name.value.length === 0){
        clearError();
        name.classList.add("error")
        name.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (email.value.length === 0) {
        clearError();
        email.classList.add("error")
        email.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (!validateEmail(email.value)) {
        clearError('Please enter a valid email');
        email.classList.add("error")
        email.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else {
        clearError();
        form.submit();
        return true;
    }
}
function clearError(message = "Please fill in all fields") {
    const name = document.querySelector('#name')
    const email = document.querySelector('#email')
    const errorMessageContainer = document.querySelector('#messageContainer')

    const errorMessage = document.querySelector('#errorMessage')
    errorMessage.innerHTML = message
    name.classList.remove("error")
    email.classList.remove("error")
    errorMessage.style.opacity = "0";
    errorMessageContainer.style.display = "none";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}

