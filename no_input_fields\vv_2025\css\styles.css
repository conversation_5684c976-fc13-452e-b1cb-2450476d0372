*, *::before, *::after {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

body, html {
    min-height: 100dvh;
    min-height: 100vh;
    font-family: '<PERSON>o', sans-serif;
}

.limiter, .container-login100, .wrap-login100 {
    height: 100%;
}

.limiter {
    display: flex;
    padding: 1rem;
}

.logo {
    display: flex;
    flex-direction: column;
    /* overflow: hidden; */
    margin-top: -50px;
    color: white;
    font-size: 2rem;
    text-align: center;
    padding-bottom: 1rem;
}

.logo img {
    width: 50%;
    max-width: 300px;
    margin: auto;
}

.logo p {
    margin-top: -50px;
}

.container-login100 {
    margin: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: end;
}

.cards {
    display: flex;
    flex-direction: column;
    background-color: white;
    width: 100%;
    max-width: 600px;
    border-radius: 1rem;
    height: 100%;
    margin: auto;
    padding-bottom: 2rem;
    padding-top: 2rem;
}

.cards p {
    text-align: center;
    font-size: 2rem;
    color: rgba(39, 39, 39, 1);
}

.cards p:nth-of-type(2) {
    margin-top: auto;
}

.cards .row {
    display: flex;
    gap: 1rem;
    padding: 1rem 2rem;
    justify-content: space-evenly;
}

.cards .row .card {
    box-shadow: 4px 0 8px 3px rgba(0, 0, 0, 0.3);
    padding: 1rem;
    border-radius: 0.5rem;
}

.cards .row .card img {
    width: 100%;
    max-width: 150px;
}

.cards .row.row-2 .card img {
    width: 100%;
    max-width: 120px;
}

.socials {
    display: flex;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.socials p {
    font-size: 1.5rem;
    font-weight: 100;
    padding-bottom: 1rem;
}

.socials img {
    width: 100%;
    max-width: 300px;
    margin: auto;
    margin-top: -100px;
    margin-bottom: -100px;
}

.wrap-login100 {
    height: auto;
    margin: 1rem;
    margin-top: 2rem;
}

.login100-form {
    display: flex;
}

button {
    padding: 0.5rem 1rem;
    border: none;
    outline: none;
    background-color: #de177a;
    color: white;
    border-radius: 0.5rem;
    font-size: 1.3rem;
    cursor: pointer;
}

button:focus, button:hover {
    background-color: #ff2e8b;
}

@media only screen and (max-width: 600px) {
    .logo img {
        max-width: 150px;
        margin-bottom: 10px;
    }

    .cards .row .card img {
        max-width: 100px;
    }

    .cards .row.row-2 .card img {
        width: 100%;
        max-width: 75px;
    }

    .logo p {
        font-size: 1.75rem;
    }

    .cards p {
        font-size: 1.25rem;
    }

    .socials p {
        font-size: 1.2rem;
    }

    .cards {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .socials img {
        margin: -40px auto;
        max-width: 150px;
    }
}
