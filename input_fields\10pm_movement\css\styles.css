:root {
    font-family: '<PERSON>', sans-serif;
}

img.top {
    position: fixed;
    width: 350px;
    top: 0;
    left: -160px;
    pointer-events: none;
}

img.bottom {
    position: fixed;
    width: 350px;
    bottom: 0;
    right: -175px;
    pointer-events: none;
}

img.dot {
    position: fixed;
    width: 50px;
    right: 300px;
    bottom: 50px;
}

.wrapper {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.header {
    padding-left: 80px;
    padding-top: 4rem;
    position: relative;
}

img.ten_pm {
    width: 200px;
}

img.logo {
    position: absolute;
    width: 75px;
    top: 0;
    right: 0;
}

img.dot.top {
    position: fixed;
    width: 50px;
    right: 0;
    top: 0;
}

.form-group {
	display: flex;
	height: 55px;
	width: 100%;
	margin-bottom: 9px;
}
.control-label {
	font-size: 16px;
	font-weight: 200;
	opacity: 1;
	pointer-events: none;
	position: absolute;
	transform: translate3d(0, 22px, 0) scale(1);
	transform-origin: left top;
	transition: 240ms;
	line-height: 29px;
}
.form-group.focused .control-label {
	opacity: 1;
	transform: scale(0.75);
	color: #002b93;
}
.form-control {
	align-self: flex-end;
}
 .form-control::-webkit-input-placeholder {
 color: transparent;
 transition: 240ms;
}
 .form-control:focus::-webkit-input-placeholder {
 transition: none;
}
 .form-group.focused .form-control::-webkit-input-placeholder {
 color: #bbb;
}

.form-group.focused label {
 color: #00bfff !important;
}
/* CSS from Bootstrap Start */
.form-control {
	display: block;
	width: 100%;
	height: 37px;
	padding: 6px 16px;
	font-size: 13px;
	line-height: 1.846;
	color: #fff;
	background-color: transparent;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	-webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
	-o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
	transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control:focus {
	border-color: #66afe9;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.form-control::-moz-placeholder {
 color: #bbbbbb;
 opacity: 1;
}
.form-control:-ms-input-placeholder {
 color: #bbbbbb;
}
.form-control::-webkit-input-placeholder {
 color: #bbbbbb;
}
.form-control::-ms-expand {
 border: 0;
 background-color: transparent;
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
	background-color: transparent;
	opacity: 1;
}
.form-control[disabled], fieldset[disabled] .form-control {
	cursor: not-allowed;
}

textarea, textarea.form-control, input.form-control, input[type=text], input[type=password], input[type=email], input[type=number], [type=text].form-control, [type=password].form-control, [type=email].form-control, [type=tel].form-control, [contenteditable].form-control {
	padding: 0;
	border: none;
	border-radius: 0;
	-webkit-appearance: none;
	-webkit-box-shadow: inset 0 -1px 0 #002b93;
	box-shadow: inset 0 -1px 0 #ffffff;
	font-size: 16px;
}
textarea:focus, textarea.form-control:focus, input.form-control:focus, input[type=text]:focus, input[type=password]:focus, input[type=email]:focus, input[type=number]:focus, [type=text].form-control:focus, [type=password].form-control:focus, [type=email].form-control:focus, [type=tel].form-control:focus, [contenteditable].form-control:focus {
	-webkit-box-shadow: inset 0 -2px 0 #00bfff;
	box-shadow: inset 0 -2px 0 #00bfff;
}
textarea[disabled], textarea.form-control[disabled], input.form-control[disabled], input[type=text][disabled], input[type=password][disabled], input[type=email][disabled], input[type=number][disabled], [type=text].form-control[disabled], [type=password].form-control[disabled], [type=email].form-control[disabled], [type=tel].form-control[disabled], [contenteditable].form-control[disabled], textarea[readonly], textarea.form-control[readonly], input.form-control[readonly], input[type=text][readonly], input[type=password][readonly], input[type=email][readonly], input[type=number][readonly], [type=text].form-control[readonly], [type=password].form-control[readonly], [type=email].form-control[readonly], [type=tel].form-control[readonly], [contenteditable].form-control[readonly] {
	-webkit-box-shadow: none;
	box-shadow: none;
	border-bottom: 1px dotted #ddd;
}
textarea.input-sm, textarea.form-control.input-sm, input.form-control.input-sm, input[type=text].input-sm, input[type=password].input-sm, input[type=email].input-sm, input[type=number].input-sm, [type=text].form-control.input-sm, [type=password].form-control.input-sm, [type=email].form-control.input-sm, [type=tel].form-control.input-sm, [contenteditable].form-control.input-sm {
	font-size: 12px;
}
textarea.input-lg, textarea.form-control.input-lg, input.form-control.input-lg, input[type=text].input-lg, input[type=password].input-lg, input[type=email].input-lg, input[type=number].input-lg, [type=text].form-control.input-lg, [type=password].form-control.input-lg, [type=email].form-control.input-lg, [type=tel].form-control.input-lg, [contenteditable].form-control.input-lg {
	font-size: 17px;
}

.error {
    background: #ff000040;
}

.error + label {
    padding-left: 1rem;
}
#errorMessage {
    color: red;
    opacity: 0;
}
/* CSS from Bootstrap End */

.checkbox-group {
    color: #001d4d;
    font-size: 1.2rem;
    margin: 1rem 0;
    display: flex;
    width: 100%;
    flex-direction: column;
    font-weight: 200;
}

.checkbox-group label {
    font-size: 1.3rem;
}

.checkbox-group span {
    margin-bottom: 0.5rem;
}

#messageContainer {
}

.input {

}

.input input {
    
}

.input input:focus {
    outline: none;
}

form#the-form {
    padding: 0 1.5rem;
    color: white;
    display: flex;
    flex-direction: column;
}

.checkbox:not(:last-of-type) {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.checkbox label {
    display: flex;
    font-weight: 300;
}

.checkbox label input {
    margin-right: 1rem;
}

.checkbox label span {
    transform: translate(0);
    z-index: 10;
}

button.connect {
    background: transparent;
    border: none;
    width: 200px;
    margin: 0 auto;
    margin-top: 4rem;
    transform: translate(0);
    z-index: 10;
}

button.connect img {
    width: 100%;
}