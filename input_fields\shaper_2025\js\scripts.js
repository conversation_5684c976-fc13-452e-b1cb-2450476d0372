function validateForm(e) {
    const username = document.querySelector('#username')
    const password = document.querySelector('#password')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')
    const form = document.querySelector("#the-form");

    if(username.value.length === 0){
        clearError();
        username.classList.add("error")
        username.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if(password.value.length === 0){
        clearError();
        password.classList.add("error")
        password.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (username.value !== 'ATSwifi@121' || password.value !== 'ATS2025') {
        clearError("The username pr password is incorrect")
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else {
        clearError();
        form.submit();
        return true;
    }
}
function clearError(message = "Please fill in your username and password") {
    const username = document.querySelector('#username')
    const errorMessageContainer = document.querySelector('#messageContainer')

    const errorMessage = document.querySelector('#errorMessage')
    errorMessage.innerHTML = message
    username.classList.remove("error")
    errorMessage.style.opacity = "0";
    errorMessageContainer.style.display = "none";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}

