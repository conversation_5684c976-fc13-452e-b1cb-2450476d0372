function validateForm(e) {
    const name = document.querySelector('#name')
    const surname = document.querySelector('#surname')
    const email = document.querySelector('#email')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')
    const form = document.querySelector("#the-form");
    
    console.log(name.value.length)
    console.log(surname.value.length)
    console.log(email.value.length)
    
    if (name.value.length === 0) {
        clearError();
        name.classList.add("error")
        name.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (surname.value.length === 0) {
        clearError();
        surname.classList.add("error")
        surname.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (email.value.length === 0) {
        clearError();
        email.classList.add("error")
        email.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (!validateEmail(email.value)) {
        clearError();
        email.classList.add("error")
        email.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else {
        console.log('form submitted')
        clearError();
        form.submit();
        return true;
    }
}
function clearError(message = "Please fill in all the fields") {
    const name = document.querySelector('#name')
    const surname = document.querySelector('#surname')
    const email = document.querySelector('#email')
    const errorMessageContainer = document.querySelector('#messageContainer')

    const errorMessage = document.querySelector('#errorMessage')
    errorMessage.innerHTML = message
    name.classList.remove("error")
    surname.classList.remove("error")
    email.classList.remove("error")
    errorMessage.style.opacity = "0";
    errorMessageContainer.style.display = "none";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}

