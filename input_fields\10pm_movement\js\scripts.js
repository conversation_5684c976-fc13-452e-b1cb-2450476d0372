function validateForm(e) {
    const name = document.querySelector('#name')
    const email = document.querySelector('#email')
    const contact = document.querySelector('#contactNumber')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')
    const form = document.querySelector("#the-form");

    const phoneNumberValidation = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./[0-9]{9,10}]*$/
    if(name.value.length === 0){
        clearError();
        name.classList.add("error")
        name.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (email.value.length === 0) {
        clearError();
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        email.focus()
        email.classList.add("error")
        return false
    } else if (!validateEmail(email.value)) {
        clearError();
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        errorMessage.innerText = 'Please enter a valid email address'
        return false
    } else if(contact.value.length === 0) {
        clearError();
        contact.classList.add("error")
        contact.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
        // } else if(cell.value.length < 10) {
    } else if(!phoneNumberValidation.test(contact.value)) {
        clearError();
        contact.classList.add("error")
        errorMessage.innerText = 'Please enter a valid cell number'
        contact.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else {
        clearError();
        form.submit();
        return true;
    }
}

function clearError() {
    const name = document.querySelector('#name')
    const email = document.querySelector('#email')
    const contact = document.querySelector('#contactNumber')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')

    errorMessage.innerText = "Please fill in all the fields"
    name.classList.remove("error")
    email.classList.remove("error")
    contact.classList.remove("error")
    errorMessage.style.opacity = "0";
    errorMessageContainer.style.display = "none";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}


