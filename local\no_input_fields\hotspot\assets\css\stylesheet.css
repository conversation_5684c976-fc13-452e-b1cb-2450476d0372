/*!
 * Start Bootstrap - Modern Business HTML Template (http://startbootstrap.com)
 * Code licensed under the Apache License v2.0.
 * For details, see http://www.apache.org/licenses/LICENSE-2.0.
 */

/* Global Styles */

html, body {
	height: 100%;
}
body {
	background: #00361f url("../images/bg.jpg") no-repeat scroll center center / cover;
	font-family: Arial, Helvetica, sans-serif;
	padding-top: 0;
	width: 100%; /* Required padding for .navbar-fixed-top. Remove if using .navbar-static-top. Change if height of navigation changes. */
}
.img-portfolio {
	margin-bottom: 30px;
}
.img-hover:hover {
	opacity: 0.8;
}
/* Home Page Carousel */

header.carousel {
	height: 50%;
}
header.carousel .item, header.carousel .item.active, header.carousel .carousel-inner {
	height: 100%;
}
header.carousel .fill {
	width: 100%;
	height: 100%;
	background-position: center;
	background-size: cover;
}
/* 404 Page Styles */

.error-404 {
	font-size: 100px;
}
/* Pricing Page Styles */

.price {
	display: block;
	font-size: 50px;
	line-height: 50px;
}
.price sup {
	top: -20px;
	left: 2px;
	font-size: 20px;
}
.period {
	display: block;
	font-style: italic;
}
/* Footer Styles */

footer {
	margin: 50px 0;
}

/* Responsive Styles */

@media(max-width:991px) {
 .customer-img,  .img-related {
 margin-bottom: 30px;
}
}
 @media(max-width:767px) {
 .img-portfolio {
 margin-bottom: 15px;
}
 header.carousel .carousel {
 height: 70%;
}
}
.side_wrapper {
	margin: 0 auto;
	max-width: 480px;
	padding: 0 30px;
	width: 100%;
}
.logo_area {
	margin:0;
	padding:20px 0 20px 0;
	width:100%;
	text-align:center;
}
.logo_area img {
	width:100%;
}
.text_field {
	background: #fff none repeat scroll 0 0;
	border: medium none;
	color: #000;
	font-size: 16px;
	font-weight: bold;
	height: 40px;
	margin: 0 0 15px;
	padding: 10px 15px;
	width: 100%;
}
.submit_btn {
	background: #1b1d1d none repeat scroll 0 0;
	border: medium none;
	color: #fff;
	cursor: pointer;
	font-size: 16px;
	font-weight: bold;
	margin: 0;
	padding: 10px 0;
	text-align: center;
	text-transform: uppercase;
	width: 100%;
}
.form_area h1 {
	margin:0;
	padding:30px 0;
	font-size:20px;
	text-align:center;
	font-weight:bold;
	font-size:20px;
	color:#FFF;
}
.footer {
    background: #ffcc08 none repeat scroll 0 0;
    bottom: 0;
    margin: 0;
    padding: 15px 0;
    position: fixed;
    text-align: center;
    width: 100%;
}
.footer img {
	width:100%;
}


.radio_btn{ margin:3px 7px 20px 0 !important; padding:0; float:left;}


.form_area p{ margin:0 0 20px 0; padding:0; font-size:14px; color:#FFF;}




::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: #000 !important;
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
   color:    #000 !important;
   opacity:  1 !important;
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
   color:    #000 !important;
   opacity:  1 !important;
}
:-ms-input-placeholder { /* Internet Explorer 10-11 */
   color:    #000 !important;
}
::-ms-input-placeholder { /* Microsoft Edge */
   color:    #000 !important;
}

.footer_logo {
    padding: 0 20px;
}


 @media (min-width: 0px) and (max-width: 768px) {
	 
.logo_area {
    margin: 0;
    padding: 10px 0;}
	 
	 
.text_field {
  background: #fff none repeat scroll 0 0;
  border: medium none;
  color: #000;
  font-size: 12px;
  font-weight: bold;
  height: 36px;
  margin: 0 0 10px;
  padding: 10px 15px;
  width: 100%;
} 

.form_area p {
  font-size: 14px;
  margin: 0 0 10px;

}

.radio_btn {
  margin: 3px 7px 10px 0 !important;
}

.submit_btn {
  font-size: 16px;
  padding: 4px 0;

}
.form_area h1 {
  font-size: 20px;
  padding: 10px 0;
 
}	 

.footer {
  padding: 10px 0;

}


	 
}



@media (min-width: 769px) and (max-width: 992px) {
	 
.logo_area {
    margin: 0;
    padding: 10px 0;}
	 
	 
.text_field {
  background: #fff none repeat scroll 0 0;
  border: medium none;
  color: #000;
  font-size: 12px;
  font-weight: bold;
  height: 36px;
  margin: 0 0 10px;
  padding: 10px 15px;
  width: 100%;
} 

.form_area p {
  font-size: 14px;
  margin: 0 0 10px;

}

.radio_btn {
  margin: 3px 7px 10px 0 !important;
}

.submit_btn {
  font-size: 16px;
  padding: 4px 0;

}
.form_area h1 {
  font-size: 20px;
  padding: 10px 0;
 
}	 

.footer {
  padding: 10px 0;

}


	 
}





@media (min-height: 0px) and (max-height: 500px) {
	
.footer {
  
    position: relative !important;
   
}
	
	
	
	}
