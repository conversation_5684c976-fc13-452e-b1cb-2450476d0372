function validateForm(e) {
    const name = document.querySelector('#name')
    const contact = document.querySelector('#contact-number')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')
    const form = document.querySelector("#the-form");

    let customerSelected = null
    let contactSelected = null


    const customerInputs = document.querySelectorAll('.checkbox-group.customer input[type="checkbox"]')
    const contactInputs = document.querySelectorAll('.checkbox-group.contact input[type="checkbox"]')

    customerInputs.forEach(function(input) {
        if (input.checked) {
            if (input.id == 'customer-yes') {
                customerSelected = 'yes'
            } else {
                customerSelected = 'no'
            }

            clearError()
        }
    })

    contactInputs.forEach(function(input) {
        if (input.checked) {
            if (input.id == 'contact-yes') {
                contactSelected = 'yes'
            } else {
                contactSelected = 'no'
            }

            clearError()
        }
    })

    const phoneNumberValidation = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./[0-9]{9,10}]*$/
    if(name.value.length === 0){
        clearError();
        name.classList.add("error")
        name.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if(contact.value.length === 0) {
        clearError();
        contact.classList.add("error")
        contact.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
        // } else if(cell.value.length < 10) {
    } else if(!phoneNumberValidation.test(contact.value)) {
        clearError();
        contact.classList.add("error")
        errorMessage.innerText = 'Please enter a valid cell number'
        contact.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (customerSelected == null) {
        clearError();
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (contactSelected == null) {
        clearError();
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else {
        clearError();
        form.submit();
        return true;
    }
  }
function clearError() {
    const name = document.querySelector('#name')
    const contact = document.querySelector('#contact-number')
    const errorMessageContainer = document.querySelector('#messageContainer')

    const errorMessage = document.querySelector('#errorMessage')
    errorMessage.innerText = "Please fill in all the fields"
    name.classList.remove("error")
    contact.classList.remove("error")
    errorMessage.style.opacity = "0";
    errorMessageContainer.style.display = "none";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}

window.onload = function () {
    const customerInputs = document.querySelectorAll('.checkbox-group.customer input[type="checkbox"]')
    const contactInputs = document.querySelectorAll('.checkbox-group.contact input[type="checkbox"]')
    const productInputs = document.querySelectorAll('.checkbox-group.product input[type="checkbox"]')

    customerInputs.forEach(function(input, key) {
        input.onclick = function (e) {
            if (key == 0 && input.checked) {
                customerInputs.item(1).checked = false
            } else if (input.checked) {
                customerInputs.item(0).checked = false
            } else {
                e.preventDefault()
            }
        }
    })

    contactInputs.forEach(function(input, key) {
        input.onclick = function (e) {
            if (key == 0 && input.checked) {
                contactInputs.item(1).checked = false
            } else if (input.checked) {
                contactInputs.item(0).checked = false
            } else {
                e.preventDefault()
            }
        }
    })

    productInputs.forEach(function(input, key) {
        input.onclick = function (e) {

        }
    })
}


