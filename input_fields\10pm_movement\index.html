<!DOCTYPE html>
<html lang="en">
<head>
<title>Login</title>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<!--===============================================================================================-->
<link rel="icon" type="image/png" href="images/icons/favicon.ico"/>
<!--===============================================================================================-->
<link rel="stylesheet" type="text/css" href="vendor/bootstrap/css/bootstrap.min.css">
<!--===============================================================================================-->

<!--===============================================================================================-->
<!--===============================================================================================-->
<!-- <link rel="stylesheet" type="text/css" href="css/util.css">
<link rel="stylesheet" type="text/css" href="css/main.css"> -->
<link rel="stylesheet" type="text/css" href="css/styles.css">
<!-- Vendor CSS-->
<link href="vendor/mdi-font/css/material-design-iconic-font.min.css" rel="stylesheet" media="all">
<!-- <link href="vendor/select2/select2.min.css" rel="stylesheet" media="all">
<link href="vendor/datepicker/daterangepicker.css" rel="stylesheet" media="all"> -->
<!--===============================================================================================-->
<style>
body {
    background-image: url(./images/background.png);
}
</style>
</head>
<body>
    <img src="./images/top.png" alt="" class="top">
    
    <div class="wrapper">
        <div class="header">
            <img src="./images/logo.png" alt="" class="logo">

            <!-- <img src="./images/dot.png" alt="" class="dot top"> -->

            <img src="./images/10pm.png" alt="" class="ten_pm">
        </div>

        <form id="the-form" method="POST" onsubmit="return validateForm(event)">
            <div class="input form-group">
                <input class="form-control" type="text" name="name" id="name">
                <label class="control-label" for="name">
                    Name and Surname
                </label>
            </div>
            
            <div class="input form-group">
                <input class="form-control" type="email" name="email" id="email">
                <label class="control-label" for="email">
                    Email Address
                </label>
            </div>

            <div class="input form-group">
                <input class="form-control" type="tel" name="contactNumber" id="contactNumber">
                <label class="control-label" for="contactNumber">
                    Contact Number
                </label>
            </div>

            <div class="checkbox">
                <label for="customer">
                    <input type="checkbox" name="customer" id="customer" value="yes">
                    <span>
                        Are you an exisitng Standard Bank customer?
                    </span>
                </label>
            </div>

            <div class="checkbox">
                <label for="communication">
                    <input type="checkbox" name="communication" id="communication" value="communication">
                    <span>
                        Opt in to receive exciting marketing communication.
                    </span>
                </label>
            </div>

            <div id="messageContainer" style="display: none;">    
                <p id="errorMessage">Please fill in all the fields</p>
            </div>

            <button class="connect" type="submit">
                <img src="./images/Connect.png" alt="">
            </button>
        </form>
    </div>

    <img src="./images/dot.png" alt="" class="dot">
    
    <img src="./images/bottom.png" alt="" class="bottom">
<script src="js/scripts.js"></script>
<script src="vendor/jquery/jquery.min.js"></script> 
<script type="text/javascript">
	/* First option in SELECT tag need to be BLANK */
/*$('.form-control').on('focus blur', function (e) {
     $(this).parents('.form-group').toggleClass('focused', (e.type === 'focus' || this.value.length > 0));
}).trigger('blur');*/

/* First option in SELECT tag don't need to be BLANK */
$('.form-control').on('focus blur change', function (e) {
	var $currEl = $(this);
  
  if($currEl.is('select')) {
  	if($currEl.val() === $("option:first", $currEl).val()) {
    	$('.control-label', $currEl.parent()).animate({opacity: 0}, 240);
      $currEl.parent().removeClass('focused');
    } else {
    	$('.control-label', $currEl.parent()).css({opacity: 1});
    	$currEl.parents('.form-group').toggleClass('focused', ((e.type === 'focus' || this.value.length > 0) && ($currEl.val() !== $("option:first", $currEl).val())));
    }
  } else {
  	$currEl.parents('.form-group').toggleClass('focused', (e.type === 'focus' || this.value.length > 0));
  }
}).trigger('blur');
</script>
</body>
</html>