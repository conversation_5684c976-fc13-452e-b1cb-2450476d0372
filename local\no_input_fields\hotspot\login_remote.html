<!DOCTYPE doctype html>
<html lang="en">
 <head>
  <link href="favicon/apple-icon-57x57.png" rel="apple-touch-icon" sizes="57x57"/>
  <link href="favicon/apple-icon-60x60.png" rel="apple-touch-icon" sizes="60x60"/>
  <link href="favicon/apple-icon-72x72.png" rel="apple-touch-icon" sizes="72x72"/>
  <link href="favicon/apple-icon-76x76.png" rel="apple-touch-icon" sizes="76x76"/>
  <link href="favicon/apple-icon-114x114.png" rel="apple-touch-icon" sizes="114x114"/>
  <link href="favicon/apple-icon-120x120.png" rel="apple-touch-icon" sizes="120x120"/>
  <link href="favicon/apple-icon-144x144.png" rel="apple-touch-icon" sizes="144x144"/>
  <link href="favicon/apple-icon-152x152.png" rel="apple-touch-icon" sizes="152x152"/>
  <link href="favicon/apple-icon-180x180.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="favicon/android-icon-192x192.png" rel="icon" sizes="192x192" type="image/png"/>
  <link href="favicon/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png"/>
  <link href="favicon/favicon-96x96.png" rel="icon" sizes="96x96" type="image/png"/>
  <link href="favicon/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png"/>
  <link href="favicon/manifest.json" rel="manifest"/>
  <meta content="#ffffff" name="msapplication-TileColor"/>
  <meta content="favicon/ms-icon-144x144.png" name="msapplication-TileImage"/>
  <meta content="#ffffff" name="theme-color"/>
  <!-- Required meta tags -->
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport"/>
  <!-- Bootstrap CSS -->
  <link href="css/bootstrap.min.css" rel="stylesheet"/>
  <!-- stylesheet -->
  <link href="css/style.css" rel="stylesheet"/>
  <title>
   Free WiFi
  </title>
  <script>
   function niceOne() {
  //Decide if there is a valid trial on the RouterBoard for the current user/MAC
		$(if trial != 'yes')
    //If there is NOT a valid trail do the following
		document.getElementById("hideIfNoTrial2").style.display = "none";
		document.getElementById("showIfNoTrial").style.display = "block";
    $(else)
    //If there is a valid trial then auto log the user in by submitting the form with action to the external login page
    document.redirect.submit();
		$(endif)
		}
  </script>
 </head>
 <body onload="niceOne()">
  <div class="container">
   <div class="d-flex minH-100vh align-items-center">
    <div class="loginSection">
     <div class="loginForm">
      <div class="form-group">
      </div>
      <!-- Form to pass RouterOS variables START-->
      <form action="http://login.quickconnect.co.za/astron" method="post" name="redirect">
       <input id="mac" name="mac" type="hidden" value="$(mac)"/>
       <input id="ip" name="ip" type="hidden" value="$(ip)"/>
       <input id="username" name="username" type="hidden" value="$(username)"/>
       <input id="link-login" name="link-login" type="hidden" value="$(link-login)"/>
       <input id="link-orig" name="link-orig" type="hidden" value="$(link-orig)"/>
       <input id="error" name="error" type="hidden" value="$(error)"/>
       <input id="chap-id" name="chap-id" type="hidden" value="$(chap-id)"/>
       <input id="chap-challenge" name="chap-challenge" type="hidden" value="$(chap-challenge)"/>
       <input id="link-login-only" name="link-login-only" type="hidden" value="$(link-login-only)"/>
       <input id="link-orig-esc" name="link-orig-esc" type="hidden"/>
       <input id="mac-esc" name="mac-esc" type="hidden" value="$(mac-esc)"/>
       <input id="routerid" name="routerid" type="hidden" value="RouterID"/>
       <input class="btn btnStyle w-100" id="hideIfNoTrial2" style="display:block;" type="submit" value="If nothing happens, click here"/>
      </form>
      <!-- Form to pass RouterOS variables END-->
      <!-- Div to display when trial time finished START-->
      <h3 class="" id="showIfNoTrial" style="display:none;">
       Seems that you have run out of allocated time for today.
       <br/>
      </h3>
      <!-- Div to display when trial time finished END-->
     </div>
     <!-- <div class="mtnIcon"><img src="images/icon-mtn.png" alt="..."></div> -->
    </div>
   </div>
  </div>
  <div id="autoSubmit">
   <script language="JavaScript">
    // document.redirect.submit();
   </script>
   <div>
    <script src="js/jquery-3.3.1.slim.min.js">
    </script>
    <script src="js/popper.min.js">
    </script>
    <script src="js/bootstrap.min.js">
    </script>
   </div>
  </div>
 </body>
</html>
