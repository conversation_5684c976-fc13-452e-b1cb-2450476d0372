function validateForm(e) {
    const firstname = document.querySelector('#firstname')
    const surname = document.querySelector('#surname')
    const email = document.querySelector('#email')
    const cell = document.querySelector('#cell')
    const errorMessage = document.querySelector('#errorMessage')
    const errorMessageContainer = document.querySelector('#messageContainer')
    const form = document.querySelector("#the-form");

    let customerSelected = null
    let consentSelected = null


    const customerInputs = document.querySelectorAll('.checkbox-group.customer input[type="checkbox"]')
    const consentInputs = document.querySelectorAll('.checkbox-group.consent input[type="checkbox"]')

    customerInputs.forEach(function(input) {
        if (input.checked) {
            if (input.id == 'customer-yes') {
                customerSelected = 'yes'
            } else {
                customerSelected = 'no'
            }

            clearError()
        }
    })

    consentInputs.forEach(function(input) {
        if (input.checked) {
            if (input.id == 'consent-yes') {
                consentSelected = 'yes'
            } else {
                consentSelected = 'no'
            }

            clearError()
        }
    })

    const phoneNumberValidation = /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./[0-9]{9,10}]*$/
    if(firstname.value.length === 0){
        clearError();
        firstname.classList.add("error")
        firstname.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if(surname.value.length === 0){
        clearError();
        surname.classList.add("error")
        surname.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } 
    else if(email.value.length === 0) {
        clearError();
        email.classList.add("error")
        email.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if (!validateEmail(email.value.trim())) {
        clearError();
        email.classList.add("error")
        errorMessage.innerText = 'Please enter a valid email'
        email.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
    } else if(cell.value.length === 0) {
        clearError();
        cell.classList.add("error")
        cell.focus()
        errorMessage.style.opacity = "1";
        errorMessageContainer.style.display = "block";
        return false
        // } else if(cell.value.length < 10) {
        } else if(!phoneNumberValidation.test(cell.value)) {
            clearError();
            cell.classList.add("error")
            errorMessage.innerText = 'Please enter a valid cell number'
            cell.focus()
            errorMessage.style.opacity = "1";
            errorMessageContainer.style.display = "block";
            return false
        } else if (customerSelected == null) {
            clearError();
            errorMessage.style.opacity = "1";
            errorMessageContainer.style.display = "block";
            return false
        } else if (consentSelected == null) {
            clearError();
            errorMessage.style.opacity = "1";
            errorMessageContainer.style.display = "block";
        return false
    } else {
            clearError();
            form.submit();
            return true;
    }
  }
function clearError() {
    const firstname = document.querySelector('#firstname')
    const surname = document.querySelector('#surname')
    const email = document.querySelector('#email')
    const cell = document.querySelector('#cell')
    const errorMessageContainer = document.querySelector('#messageContainer')

    const errorMessage = document.querySelector('#errorMessage')
    errorMessage.innerText = "Please fill in all the fields"
    firstname.classList.remove("error")
    surname.classList.remove("error")
    email.classList.remove("error")
    cell.classList.remove("error")
    errorMessage.style.opacity = "0";
    errorMessageContainer.style.display = "none";
}

function validateEmail(mail) 
{
 if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(mail))
  {
    return (true)
  }
    return (false)
}

window.onload = function () {
    const customerInputs = document.querySelectorAll('.checkbox-group.customer input[type="checkbox"]')
    const consentInputs = document.querySelectorAll('.checkbox-group.consent input[type="checkbox"]')

    customerInputs.forEach(function(input, key) {
        input.onclick = function (e) {
            if (key == 0 && input.checked) {
                customerInputs.item(1).checked = false
            } else if (input.checked) {
                customerInputs.item(0).checked = false
            } else {
                e.preventDefault()
            }
        }
    })

    consentInputs.forEach(function(input, key) {
        input.onclick = function (e) {
            if (key == 0 && input.checked) {
                consentInputs.item(1).checked = false
            } else if (input.checked) {
                consentInputs.item(0).checked = false
            } else {
                e.preventDefault()
            }
        }
    })
}


