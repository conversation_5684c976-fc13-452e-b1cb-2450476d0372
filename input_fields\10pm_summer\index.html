<!DOCTYPE html>
<html lang="en">
<head>
<title>Login</title>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<!--===============================================================================================-->
<link rel="icon" type="image/png" href="images/icons/favicon.ico"/>
<!--===============================================================================================-->
<link rel="stylesheet" type="text/css" href="vendor/bootstrap/css/bootstrap.min.css">
<!--===============================================================================================-->

<!--===============================================================================================-->
<!--===============================================================================================-->
<link rel="stylesheet" type="text/css" href="css/util.css">
<link rel="stylesheet" type="text/css" href="css/main.css">
<!-- Vendor CSS-->
<link href="vendor/mdi-font/css/material-design-iconic-font.min.css" rel="stylesheet" media="all">
<link href="vendor/select2/select2.min.css" rel="stylesheet" media="all">
<link href="vendor/datepicker/daterangepicker.css" rel="stylesheet" media="all">
<!--===============================================================================================-->
<style>
.form-group {
	display: flex;
	height: 55px;
	width: 100%;
	margin-bottom: 9px;
}
.control-label {
	font-family: 'Benton Sans', sans-serif;
	font-size: 16px;
	font-weight: 400;
	opacity: 1;
	pointer-events: none;
	position: absolute;
	transform: translate3d(0, 22px, 0) scale(1);
	transform-origin: left top;
	transition: 240ms;
	line-height: 29px;
}
.form-group.focused .control-label {
	opacity: 1;
	transform: scale(0.75);
	color: #002b93;
}
.form-control {
	align-self: flex-end;
}
 .form-control::-webkit-input-placeholder {
 color: transparent;
 transition: 240ms;
}
 .form-control:focus::-webkit-input-placeholder {
 transition: none;
}
 .form-group.focused .form-control::-webkit-input-placeholder {
 color: #bbb;
}

.form-group.focused label {
 color: #00bfff !important;
}
/* CSS from Bootstrap Start */
.form-control {
	display: block;
	width: 100%;
	height: 37px;
	padding: 6px 16px;
	font-size: 13px;
	line-height: 1.846;
	color: #666666;
	background-color: transparent;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	-webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
	-o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
	transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control:focus {
	border-color: #66afe9;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.form-control::-moz-placeholder {
 color: #bbbbbb;
 opacity: 1;
}
.form-control:-ms-input-placeholder {
 color: #bbbbbb;
}
.form-control::-webkit-input-placeholder {
 color: #bbbbbb;
}
.form-control::-ms-expand {
 border: 0;
 background-color: transparent;
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
	background-color: transparent;
	opacity: 1;
}
.form-control[disabled], fieldset[disabled] .form-control {
	cursor: not-allowed;
}
textarea.form-control {
	height: auto;
}
input[type="search"] {
	-webkit-appearance: none;
}
textarea, textarea.form-control, input.form-control, input[type=text], input[type=password], input[type=email], input[type=number], [type=text].form-control, [type=password].form-control, [type=email].form-control, [type=tel].form-control, [contenteditable].form-control {
	padding: 0;
	border: none;
	border-radius: 0;
	-webkit-appearance: none;
	-webkit-box-shadow: inset 0 -1px 0 #002b93;
	box-shadow: inset 0 -1px 0 #002b93;
	font-size: 16px;
}
textarea:focus, textarea.form-control:focus, input.form-control:focus, input[type=text]:focus, input[type=password]:focus, input[type=email]:focus, input[type=number]:focus, [type=text].form-control:focus, [type=password].form-control:focus, [type=email].form-control:focus, [type=tel].form-control:focus, [contenteditable].form-control:focus {
	-webkit-box-shadow: inset 0 -2px 0 #00bfff;
	box-shadow: inset 0 -2px 0 #00bfff;
}
textarea[disabled], textarea.form-control[disabled], input.form-control[disabled], input[type=text][disabled], input[type=password][disabled], input[type=email][disabled], input[type=number][disabled], [type=text].form-control[disabled], [type=password].form-control[disabled], [type=email].form-control[disabled], [type=tel].form-control[disabled], [contenteditable].form-control[disabled], textarea[readonly], textarea.form-control[readonly], input.form-control[readonly], input[type=text][readonly], input[type=password][readonly], input[type=email][readonly], input[type=number][readonly], [type=text].form-control[readonly], [type=password].form-control[readonly], [type=email].form-control[readonly], [type=tel].form-control[readonly], [contenteditable].form-control[readonly] {
	-webkit-box-shadow: none;
	box-shadow: none;
	border-bottom: 1px dotted #ddd;
}
textarea.input-sm, textarea.form-control.input-sm, input.form-control.input-sm, input[type=text].input-sm, input[type=password].input-sm, input[type=email].input-sm, input[type=number].input-sm, [type=text].form-control.input-sm, [type=password].form-control.input-sm, [type=email].form-control.input-sm, [type=tel].form-control.input-sm, [contenteditable].form-control.input-sm {
	font-size: 12px;
}
textarea.input-lg, textarea.form-control.input-lg, input.form-control.input-lg, input[type=text].input-lg, input[type=password].input-lg, input[type=email].input-lg, input[type=number].input-lg, [type=text].form-control.input-lg, [type=password].form-control.input-lg, [type=email].form-control.input-lg, [type=tel].form-control.input-lg, [contenteditable].form-control.input-lg {
	font-size: 17px;
}
select, select.form-control {
	border: 0;
	border-radius: 0;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	padding-left: 0;
	padding-right: 0\9;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=);
	-webkit-background-size: 13px 13px;
	background-size: 13px;
	background-repeat: no-repeat;
	background-position: right center;
	-webkit-box-shadow: inset 0 -1px 0 #dddddd;
	box-shadow: inset 0 -1px 0 #dddddd;
	font-size: 16px;
	line-height: 1.5;
}
select::-ms-expand, select.form-control::-ms-expand {
 display: none;
}
select.input-sm, select.form-control.input-sm {
	font-size: 12px;
}
select.input-lg, select.form-control.input-lg {
	font-size: 17px;
}
select:focus, select.form-control:focus {
	-webkit-box-shadow: inset 0 -2px 0 #002b93;
	box-shadow: inset 0 -2px 0 #002b93;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEUhISEhISEhISEhISEhISEhISEhISEhISEhISEhISEhISEhISEhISF8S9ewAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=);
}
select[multiple], select.form-control[multiple] {
	background: none;
}

.error {
    background: #ff000040;
}

.error + label {
    padding-left: 1rem;
}
#errorMessage {
    color: red;
    opacity: 0;
}
/* CSS from Bootstrap End */

.checkbox-group {
    color: #001d4d;
    font-size: 1.2rem;
    margin: 1rem 0;
    display: flex;
    width: 100%;
    flex-direction: column;
    font-weight: 200;
}

.checkbox-group label {
    font-size: 1.3rem;
}

.checkbox-group span {
    margin-bottom: 0.5rem;
}

#messageContainer {
}
</style>
</head>
<body>
<div class="limiter">
  <div class="container-login100">
    <div class="wrap-login100">
      <center>
        <img src="images/Header.png" class="masth">
      </center>
      <!-- action="https://postman-echo.com/post" -->
      <form class="login100-form validate-form" method="POST" id="the-form" onsubmit="return validateForm(event)">
        <div class="form-group">
            <input type="text" class="form-control" onchange="clearError()" id="name" name="name"/>
            <label class="control-label" for="inputNormal">Name and Surname</label>
        </div>
        <div class="form-group">
            <input type="tel" class="form-control" onchange="clearError()" id="contact-number" name="contact-number"/>
            <label class="control-label" for="inputNormal">Contact Number</label>
        </div><br><br><br>

        <div class="checkbox-group customer" style="margin-top: 1rem;">
            <span style="display: block;">Are you a Standard Bank customer?</span>
            
            <label for="customer-yes">
                <input type="checkbox" name="customer-yes" id="customer-yes">
                Yes
            </label>

            <label for="customer-no">
                <input type="checkbox" name="customer-no" id="customer-no">
                No
            </label>
        </div>

        <div class="checkbox-group product">
            <span style="display: block;">What Standard Bank product are you interested in?</span>
            
            <label for="product-mymo">
                <input type="checkbox" name="products" id="product-mymo" value="MyMo">
                MyMo
            </label>

            <label for="product-flexi-funeral">
                <input type="checkbox" name="products" id="product-flexi-funeral" value="Flexi Funeral">
                Flexi Funeral
            </label>

            <label for="product-student-loans">
                <input type="checkbox" name="products" id="product-student-loans" value="Student Loans">
                Student Loans
            </label>

            <label for="product-digital-adaptation">
                <input type="checkbox" name="products" id="product-digital-adaptation" value="Digital Adaptation">
                Digital Adaptation
            </label>
        </div>

        <div class="checkbox-group contact">
            <span style="display: block;">Do you agree to be contacted by a Standard Bank Consultant?</span>
            
            <label for="contact-yes">
                <input type="checkbox" name="contact-yes" id="contact-yes">
                Yes
            </label>

            <label for="contact-no">
                <input type="checkbox" name="contact-no" id="contact-no">
                No
            </label>
        </div>

        <!-- <div class="input-group" style="border-bottom:#FFFFFF;padding-top:10px;margin-top: 25px;margin-bottom:20px;">
          <div class="lab">
            <label class="lb-media">
              <input type='checkbox' name="existing-customer" style="float: left;margin-right: 10px;">
              <span style="float:left;margin-right: 10px;"></span>Are you an existing Standard Bank customer?</label>
          </div>
        </div>
        <div class="input-group" style="border-bottom:#FFFFFF;padding-top:10px;">
          <div class="lab">
            <label class="lb-media">
              <input type='checkbox' name="receive-marketing" style="float: left;margin-right: 10px;">
              <span style="float:left;margin-right: 10px;"></span>Opt in to receive exciting marketing communication.</label>
          </div> -->
        </div>
        <div style="display: flex; flex-direction: column; width: 100%; max-width: 15rem;">
            <div id="messageContainer" style="display: none;">    
                <p id="errorMessage">Please fill in all the fields</p>
            </div>
            <button class="btn btn--radius btn--green" type="submit" form="the-form">CONNECT</button>
        </div>
      </form>
    </div>
  </div>
</div>
<script src="js/scripts.js"></script>
<script src="vendor/jquery/jquery.min.js"></script> 
<script type="text/javascript">
	/* First option in SELECT tag need to be BLANK */
/*$('.form-control').on('focus blur', function (e) {
     $(this).parents('.form-group').toggleClass('focused', (e.type === 'focus' || this.value.length > 0));
}).trigger('blur');*/

/* First option in SELECT tag don't need to be BLANK */
$('.form-control').on('focus blur change', function (e) {
	var $currEl = $(this);
  
  if($currEl.is('select')) {
  	if($currEl.val() === $("option:first", $currEl).val()) {
    	$('.control-label', $currEl.parent()).animate({opacity: 0}, 240);
      $currEl.parent().removeClass('focused');
    } else {
    	$('.control-label', $currEl.parent()).css({opacity: 1});
    	$currEl.parents('.form-group').toggleClass('focused', ((e.type === 'focus' || this.value.length > 0) && ($currEl.val() !== $("option:first", $currEl).val())));
    }
  } else {
  	$currEl.parents('.form-group').toggleClass('focused', (e.type === 'focus' || this.value.length > 0));
  }
}).trigger('blur');
</script>
</body>
</html>